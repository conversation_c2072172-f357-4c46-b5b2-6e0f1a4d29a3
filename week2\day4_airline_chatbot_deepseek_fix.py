import os
import json
from dotenv import load_dotenv
from openai import OpenAI
import gradio as gr

load_dotenv(override=True)

shivaay_api_key = os.getenv("OPENAI_API_KEY")
shivaay_base_url = "https://api.futurixai.com/api/shivaay/v1"

shivaay_via_openai = OpenAI(
    api_key=shivaay_api_key,
    base_url=shivaay_base_url,
)

ticket_prices = {
    "New Delhi": 1000,
    "Bangaluru": 7000,
    "Mumbai": 3000,
    "Delhi": 1000,
    "Bangalore": 7000,
    "Bengaluru": 7000
}

def get_ticket_price(destination_city):
    print(f"🔧 Tool get_ticket_price called with: '{destination_city}'")
    price = ticket_prices.get(destination_city)
    if price:
        return f"The return ticket to {destination_city} costs ₹{price}."
    else:
        return f"I'm sorry, we don't fly to {destination_city}."

price_function = {
    "name": "get_ticket_price",
    "description": "Get the price of a return ticket to the destination city. Call this whenever a customer asks about ticket prices to a specific city.",
    "parameters": {
        "type": "object",
        "properties": {
            "destination_city": {
                "type": "string",
                "description": "The name of the destination city (e.g., 'Mumbai', 'New Delhi', 'Bangaluru')"
            }
        },
        "required": ["destination_city"],
        "additionalProperties": False
    }
}

tools = [{"type": "function", "function": price_function}]

def handle_tool_call(message):
    """Handle function tool calls"""
    print(f"🛠️ Handling tool call: {message.tool_calls}")
    
    if not message.tool_calls:
        print("❌ No tool calls found in message")
        return None, None
    
    tool_call = message.tool_calls[0]
    arguments = json.loads(tool_call.function.arguments)
    city = arguments.get('destination_city')
    print(f"📨 Extracted city: {city}")
    
    price_response = get_ticket_price(city)
    
    tool_response = {
        "role": "tool",
        "content": price_response,
        "tool_call_id": tool_call.id
    }
    return tool_response, city

system_message = """You are a helpful assistant for an Airline called FlightAI.
Give short, courteous answers, no more than 1-2 sentences.
Always be accurate. If you don't know the answer, say so.
Use the get_ticket_price function when customers ask about ticket prices to specific cities like Mumbai, Delhi, or Bangaluru."""

def Chat(message, history):
    """Main chat function with tool calling"""
    print(f"\n=== NEW MESSAGE ===")
    print(f"📩 User: {message}")
    
    # Convert Gradio history format to OpenAI format
    openai_history = []
    for human_msg, ai_msg in history:
        openai_history.append({"role": "user", "content": human_msg})
        openai_history.append({"role": "assistant", "content": ai_msg})
    
    # Build messages list
    messages = [
        {"role": "system", "content": system_message},
        *openai_history,
        {"role": "user", "content": message}
    ]
    
    print("📤 Sending messages to API...")
    
    try:
        # First API call - with tools enabled
        response = shivaay_via_openai.chat.completions.create(
            model="shivaay",
            messages=messages,
            tools=tools,
            tool_choice="auto",  # Explicitly enable tool choice
            max_tokens=300
        )
        
        message_response = response.choices[0].message
        finish_reason = response.choices[0].finish_reason
        
        print(f"✅ API Response - Finish reason: {finish_reason}")
        print(f"📝 Message content: {message_response.content}")
        print(f"🔧 Tool calls: {message_response.tool_calls}")
        
        # Check if tool calling is required
        if finish_reason == "tool_calls" and message_response.tool_calls:
            print("🔄 Tool call detected! Processing...")
            
            # Add the assistant's tool call request to messages
            messages.append({
                "role": "assistant",
                "content": message_response.content,
                "tool_calls": [
                    {
                        "id": tool_call.id,
                        "type": "function",
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    } for tool_call in message_response.tool_calls
                ]
            })
            
            # Handle the tool call and get the response
            tool_response, city = handle_tool_call(message_response)
            if tool_response:
                messages.append(tool_response)
                
                print("📤 Sending tool response back to API...")
                
                # Second API call - send the tool response back to the model
                second_response = shivaay_via_openai.chat.completions.create(
                    model="shivaay",
                    messages=messages,
                    tools=tools,  # Keep tools available
                    max_tokens=300
                )
                
                final_response = second_response.choices[0].message.content
                print(f"🎯 Final response: {final_response}")
                return final_response
            else:
                return "I encountered an error processing your request."
        
        # If no tool calls, return the direct response
        final_response = message_response.content
        print(f"🎯 Direct response: {final_response}")
        return final_response if final_response else "I don't have a response for that."
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return f"I'm having trouble connecting to the service. Error: {str(e)}"

# Test function to verify tool calling works
def test_tool_calling():
    """Test if tool calling works with specific prompts"""
    test_prompts = [
        "How much is a ticket to Mumbai?",
        "What's the price for a flight to Delhi?",
        "Tell me the cost of a ticket to Bangaluru",
        "I want to know the fare to Mumbai"
    ]
    
    for prompt in test_prompts:
        print(f"\n🧪 Testing: {prompt}")
        try:
            response = shivaay_via_openai.chat.completions.create(
                model="shivaay",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                tools=tools,
                tool_choice="auto",
                max_tokens=100
            )
            
            finish_reason = response.choices[0].finish_reason
            tool_calls = response.choices[0].message.tool_calls
            
            print(f"   Finish reason: {finish_reason}")
            print(f"   Tool calls: {bool(tool_calls)}")
            if tool_calls:
                print(f"   Function called: {tool_calls[0].function.name}")
                
        except Exception as e:
            print(f"   Error: {e}")

if __name__ == "__main__":
    print("🔍 Testing tool calling support...")
    test_tool_calling()
    
    print("\n" + "="*50)
    print("🚀 Starting Gradio interface...")
    
    demo = gr.ChatInterface(
        fn=Chat,
        title="✈️ FlightAI Assistant with Tool Calling",
        description="Ask me about ticket prices! I can check prices for Mumbai, Delhi, and Bangaluru.",
        examples=[
            "How much is a ticket to Mumbai?",
            "What's the price for Delhi?",
            "Tell me the cost to Bangaluru"
        ],
        theme="soft"
    )
    
    demo.launch(share=True, inbrowser=True)